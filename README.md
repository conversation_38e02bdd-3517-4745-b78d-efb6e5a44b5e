# Community Waste Collection Smart Contract

A decentralized waste management system built on the Stacks blockchain that incentivizes proper waste disposal through token rewards.

## Overview

The Community Waste Collection smart contract implements a comprehensive waste management system that:

- **Registers households** in the waste collection program
- **Tracks waste collection** with payment verification
- **Rewards participants** with SUSTAIN tokens for proper waste disposal
- **Implements SIP-010** fungible token standard for seamless token transfers
- **Provides admin functions** for fund management

## Features

### 🏠 Household Registration
- Simple registration process for households
- Prevents duplicate registrations
- Tracks household participation metrics

### ♻️ Waste Reporting & Payment
- Report waste collection with weight (kg) and fee rate
- Automatic STX payment processing
- Real-time tracking of waste collected and payments made

### 🪙 Token Rewards
- Earn 10 SUSTAIN tokens per kg of waste collected
- Full SIP-010 compliance for token transfers
- Transparent token supply management

### 📊 Analytics & Tracking
- Total waste collected across all households
- Total STX payments received
- Individual household statistics
- Token balance tracking

### 🔐 Admin Functions
- Secure fund withdrawal for project operations
- Contract owner verification
- Balance validation before withdrawals

## Smart Contract Functions

### Public Functions

#### Registration
- `register-household()` - Register a household in the system

#### Waste Management
- `report-and-pay(waste-kg, fee-per-kg)` - Report waste collection and process payment

#### Token Operations (SIP-010)
- `transfer(amount, from, to, memo)` - Transfer SUSTAIN tokens
- `get-balance(who)` - Get token balance for an address
- `get-total-supply()` - Get total token supply

#### Admin
- `withdraw(amount)` - Withdraw STX from contract (owner only)

### Read-Only Functions

#### Token Information
- `get-name()` - Returns "SUSTAIN Token"
- `get-symbol()` - Returns "SUSTAIN"
- `get-decimals()` - Returns 6 decimals
- `get-token-uri()` - Returns token metadata URI

#### System Information
- `get-household-info(address)` - Get household registration and stats
- `get-total-waste-collected()` - Get total waste collected system-wide
- `get-total-stx-received()` - Get total STX payments received
- `get-contract-owner()` - Get contract owner address

## Technical Specifications

### Token Details
- **Name**: SUSTAIN Token
- **Symbol**: SUSTAIN
- **Decimals**: 6
- **Standard**: SIP-010 Fungible Token

### Reward Mechanism
- **Rate**: 10 SUSTAIN tokens per kg of waste
- **Payment**: STX required for waste reporting
- **Calculation**: `required_fee = waste_kg * fee_per_kg`

### Error Codes
- `u100` - Owner only operation
- `u101` - Not token owner
- `u102` - Insufficient balance
- `u103` - Invalid amount
- `u401` - Household not registered
- `u403` - Unauthorized access
- `u404` - Not found
- `u409` - Already registered

## Getting Started

### Prerequisites
- [Clarinet](https://github.com/hirosystems/clarinet) - Stacks smart contract development tool
- [Node.js](https://nodejs.org/) - For running tests (optional)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/itzbayo/community-waste-collection.git
cd community-waste-collection
```

2. Verify contract syntax:
```bash
clarinet check
```

3. Run interactive console:
```bash
clarinet console
```

### Testing

The contract has been thoroughly tested and passes all Clarinet checks:

```bash
# Check contract syntax and types
clarinet check

# Run in interactive mode for testing
clarinet console
```

### Example Usage

```clarity
;; Register a household
(contract-call? .community_waste register-household)

;; Report 5kg of waste at 1000 micro-STX per kg
(contract-call? .community_waste report-and-pay u5 u1000)

;; Check household info
(contract-call? .community_waste get-household-info 'ST1PQHQKV0RJXZFY1DGX8MNSNYVE3VGZJSRTPGZGM)

;; Check token balance (should be 50 tokens for 5kg)
(contract-call? .community_waste get-balance 'ST1PQHQKV0RJXZFY1DGX8MNSNYVE3VGZJSRTPGZGM)

;; Transfer tokens
(contract-call? .community_waste transfer u10 'ST1PQHQKV0RJXZFY1DGX8MNSNYVE3VGZJSRTPGZGM 'ST1SJ3DTE5DN7X54YDH5D64R3BCB6A2AG2ZQ8YPD5 none)
```

## Project Structure

```
community-waste-collection/
├── contracts/
│   └── community_waste.clar     # Main smart contract
├── settings/
│   ├── Devnet.toml             # Development network settings
│   ├── Mainnet.toml            # Mainnet settings
│   └── Testnet.toml            # Testnet settings
├── tests/
│   └── community_waste.test.ts # TypeScript tests
├── Clarinet.toml               # Clarinet configuration
├── package.json                # Node.js dependencies
├── tsconfig.json              # TypeScript configuration
├── vitest.config.js           # Test configuration
└── README.md                  # This file
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contact

- GitHub: [@itzbayo](https://github.com/itzbayo)
- Project Link: [https://github.com/itzbayo/community-waste-collection](https://github.com/itzbayo/community-waste-collection)

## Acknowledgments

- Built on [Stacks](https://www.stacks.co/) blockchain
- Uses [Clarinet](https://github.com/hirosystems/clarinet) development tools
- Implements [SIP-010](https://github.com/stacksgov/sips/blob/main/sips/sip-010/sip-010-fungible-token-standard.md) token standard
